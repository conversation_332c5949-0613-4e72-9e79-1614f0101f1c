<template>
  <div>
    <!-- Navigation Menu -->
    <nav class="bg-white shadow-sm border-b">
      <UContainer class="py-4">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-bold text-gray-900">My Website</h2>
          
          <div class="flex space-x-6">
            <NuxtLink to="/" class="text-gray-600 hover:text-blue-600 font-medium">
              หน้าแรก
            </NuxtLink>
            <NuxtLink to="/about" class="text-gray-600 hover:text-blue-600 font-medium">
              เกี่ยวกับเรา
            </NuxtLink>
            <NuxtLink to="/contact" class="text-blue-600 font-medium">
              ติดต่อเรา
            </NuxtLink>
          </div>
        </div>
      </UContainer>
    </nav>

    <!-- เนื้อหาหน้าติดต่อเรา -->
    <UContainer class="py-16">
      <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8 text-center">
          ติดต่อเรา
        </h1>
        
        <!-- ฟอร์มติดต่อ -->
        <UCard>
          <template #header>
            <h2 class="text-xl font-semibold">ส่งข้อความถึงเรา</h2>
          </template>
          
          <form class="space-y-4">
            <!-- ช่องกรอกชื่อ -->
            <UFormGroup label="ชื่อ-นามสกุล" required>
              <UInput placeholder="กรุณากรอกชื่อของคุณ" />
            </UFormGroup>
            
            <!-- ช่องกรอกอีเมล -->
            <UFormGroup label="อีเมล" required>
              <UInput type="email" placeholder="<EMAIL>" />
            </UFormGroup>
            
            <!-- ช่องกรอกหัวข้อ -->
            <UFormGroup label="หัวข้อ" required>
              <UInput placeholder="หัวข้อที่ต้องการติดต่อ" />
            </UFormGroup>
            
            <!-- ช่องกรอกข้อความ -->
            <UFormGroup label="ข้อความ" required>
              <UTextarea 
                placeholder="กรุณาระบุรายละเอียดที่ต้องการติดต่อ..." 
                rows="5"
              />
            </UFormGroup>
            
            <!-- ปุ่มส่ง -->
            <div class="flex justify-end space-x-3">
              <UButton 
                label="ยกเลิก" 
                color="gray" 
                variant="outline"
                to="/"
              />
              <UButton 
                label="ส่งข้อความ" 
                color="primary" 
                variant="solid"
              />
            </div>
          </form>
        </UCard>
      </div>
    </UContainer>
  </div>
</template>
