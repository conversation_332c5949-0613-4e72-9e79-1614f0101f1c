<template>
  <!-- 
    UContainer เป็น Component จาก Nuxt UI Pro 
    ช่วยจัดให้เนื้อหาอยู่ตรงกลางหน้าจอ และปรับขนาดตามความกว้างของจออัตโนมัติ
  -->
  <UContainer class="py-10">
    <!-- 
      h1 คือแท็กสำหรับหัวข้อหลักของหน้าเว็บ
      class="text-2xl font-bold" เป็นการใช้ Utility Classes ของ Tailwind CSS (ที่มาพร้อมกับ Nuxt UI)
      เพื่อกำหนดขนาดตัวอักษรและความหนา
    -->
    <h1 class="text-2xl font-bold">ยินดีต้อนรับสู่โปรเจกต์แรกของฉัน</h1>

    <!-- 
      div class="mt-4" คือการสร้างกล่องและเว้นระยะห่างจากด้านบน (margin-top)
    -->
    <div class="mt-4">
      <p>เรากำลังจะเริ่มสร้างเว็บด้วย Nuxt.js และ Nuxt UI Pro</p>
    </div>

    <div class="mt-8 flex items-center space-x-4">
      <!-- 
        UButton คือ Component ปุ่มจาก Nuxt UI Pro
        - label: คือข้อความที่จะแสดงบนปุ่ม
        - color: คือสีของปุ่ม (มีให้เลือกหลายสี เช่น primary, gray, red)
        - variant: คือรูปแบบของปุ่ม (solid, outline, soft, ghost)
        - size: คือขนาดของปุ่ม (xs, sm, md, lg, xl)
      -->
      <UButton label="เริ่มต้นใช้งาน" color="primary" variant="solid" size="lg" />

      <!-- 
        UBadge คือ Component ป้ายข้อความ
      -->
      <UBadge label="เวอร์ชัน 1.0" color="gray" variant="soft" />
    </div>

  </UContainer>
</template>

<style>
/* 
  เราสามารถเขียน CSS เพิ่มเติมสำหรับตกแต่งหน้าเว็บได้ที่นี่
  body { background-color: #f9fafb; } 
*/
</style>
