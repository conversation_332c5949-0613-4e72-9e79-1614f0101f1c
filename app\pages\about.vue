<template>
  <div>
    <!-- Navigation Menu (เหมือนหน้าแรก) -->
    <nav class="bg-white shadow-sm border-b">
      <UContainer class="py-4">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-bold text-gray-900">My Website</h2>
          
          <div class="flex space-x-6">
            <NuxtLink to="/" class="text-gray-600 hover:text-blue-600 font-medium">
              หน้าแรก
            </NuxtLink>
            <NuxtLink to="/about" class="text-blue-600 font-medium">
              เกี่ยวกับเรา
            </NuxtLink>
            <NuxtLink to="/contact" class="text-gray-600 hover:text-blue-600 font-medium">
              ติดต่อเรา
            </NuxtLink>
          </div>
        </div>
      </UContainer>
    </nav>

    <!-- เนื้อหาหน้าเกี่ยวกับเรา -->
    <UContainer class="py-16">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8 text-center">
          เกี่ยวกับเรา
        </h1>
        
        <div class="grid md:grid-cols-2 gap-8 items-center">
          <!-- ข้อความด้านซ้าย -->
          <div>
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">
              เราคือใคร?
            </h2>
            <p class="text-gray-600 mb-4">
              เราเป็นทีมพัฒนาเว็บไซต์ที่มีความหลงใหลในเทคโนโลยี Frontend 
              โดยเฉพาะ Nuxt.js และ Vue.js
            </p>
            <p class="text-gray-600 mb-6">
              เป้าหมายของเราคือการสร้างเว็บไซต์ที่สวยงาม ใช้งานง่าย 
              และมีประสิทธิภาพสูง
            </p>
            
            <UButton 
              label="กลับหน้าแรก" 
              color="primary" 
              variant="outline"
              to="/"
            />
          </div>
          
          <!-- การ์ดข้อมูลด้านขวา -->
          <div class="space-y-4">
            <UCard>
              <template #header>
                <h3 class="text-lg font-semibold">เทคโนโลยีที่เราใช้</h3>
              </template>
              
              <div class="space-y-2">
                <UBadge label="Nuxt.js" color="green" variant="soft" />
                <UBadge label="Vue.js" color="green" variant="soft" />
                <UBadge label="Nuxt UI" color="blue" variant="soft" />
                <UBadge label="Tailwind CSS" color="cyan" variant="soft" />
              </div>
            </UCard>
          </div>
        </div>
      </div>
    </UContainer>
  </div>
</template>
